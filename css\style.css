@font-face {
    font-family: 'Concrete';
    src: url('../fonts/Concrete-2.ttf') format('truetype');
    /* src: url('../fonts/MinecraftRegular-Bmg3.otf') format('truetype'); */
    font-weight: normal;
    font-style: normal;
}

/* 基础样式 */
body {
    background-attachment: fixed;
    background-repeat: no-repeat;
    position: relative;
    background-color: black;
    font-family: Concrete, Arial, Helvetica, sans-serif;
    overflow: hidden;
    height: 100vh;
    margin: 0;
    padding: 0;
}

/* main 容器样式 */
.main {
    max-width: 600px;
    padding: 10px;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    height: auto;
    max-height: 90vh;
    overflow-y: auto;
    width: 100%;
}

/* 标题样式 */
h1 {
    color: #e84393;
    font-size: 4.0rem;
    position: relative;
    margin: 0;
}

h1::before {
    content: 'METAQIU\'S HOME';
    position: absolute;
    color: #b2bec348;
    z-index: -1;
    left: 10px;
    top: 10px;
}

/* 内容样式 */
.content {
    padding: 20px 0;
    flex: 0 0 auto;
}

p {
    color: #b2bec3;
    font-size: 1.6rem;
    margin: 0 0 10px 0;
    line-height: 1.8;
}

/* 社交链接样式 */
.social-links {
    text-align: center;
    margin: 30px 0;
}

.social-item {
    display: inline-block;
    margin: 0 15px;
    font-size: 1.8rem;
    color: #b2bec3;
}

/* 简单的颜色过渡效果 */
.social-item:hover,
.content p:hover {
    color: #e84393;
}

/* 背景区域样式 */
.background-area {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.486);
    z-index: -1;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.background-area img {
    filter: blur(20px);
    min-width: 100%;
    min-height: 100%;
    max-width: 200%;
    max-height: 200%;
}

/* 页脚样式 */
.footer {
    margin-top: auto;
    padding: 15px 0;
    color: #999;
    font-family: Arial;
    font-size: 0.9rem;
    line-height: 1.5;
}

.footer a {
    color: #999;
}

/* 响应式布局 */
@media screen and (max-width: 600px) {
    .main {
        width: 90%;
        padding: 15px;
        max-height: 90vh;
        transform: translate(-50%, -50%);
    }
    
    h1 {
        font-size: 3rem;
    }
    
    p {
        font-size: 1.2rem;
    }
}

/* 自我介绍部分样式 */
.intro-section {
    margin-top: 40px;
    padding: 30px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.intro-text {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.95);
    text-align: center;
}

.intro-detail {
    font-size: 1.1rem;
    font-weight: 400;
    line-height: 1.8;
    margin: 12px 0;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.85);
    text-align: center;
    padding: 8px 0;
}

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 标题动画 */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 内容动画 */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 社交图标动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 应用动画到元素 */
.title.animate {
    opacity: 0;
    animation: slideInFromTop 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.content p.animate {
    opacity: 0;
    animation: slideInFromRight 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    transition: all 0.3s ease;
}

.content p.animate:nth-child(1) {
    animation-delay: 0.3s;
}

.content p.animate:nth-child(2) {
    animation-delay: 0.5s;
}

.content p.animate:nth-child(3) {
    animation-delay: 0.7s;
}

.intro-section.animate {
    opacity: 0;
    animation: fadeIn 1s ease forwards;
    animation-delay: 0.9s;
}

.social-links.animate {
    opacity: 0;
    animation: fadeInUp 0.8s ease forwards;
    animation-delay: 1.1s;
}

/* 标题悬停效果 */
h1 {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: default;
}

h1:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 8px 16px rgba(255, 255, 255, 0.2));
}

/* 修改自我介绍悬停效果 */
.intro-detail {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: default;
}

/* 为第2、3、4个intro-detail添加删除线 */
.intro-detail:nth-child(n+2):nth-child(-n+4) {
    text-decoration: line-through;
    text-decoration-color: rgba(255, 255, 255, 0.4);
    text-decoration-thickness: 2px;
}

.intro-detail:hover {
    color: rgba(255, 255, 255, 1);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 运行时间样式 */
#runtime {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 15px;
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    font-weight: 500;
    transition: all 0.3s ease;
}

#runtime:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 添加滚动条样式 */
.main::-webkit-scrollbar {
    width: 6px;
}

.main::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.main::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.main::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 添加微妙的入场动画 */
.main {
    animation: containerFadeIn 1.2s ease-out forwards;
}

@keyframes containerFadeIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 添加文字选择样式 */
::selection {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

::-moz-selection {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

/* 添加焦点样式 */
.social-item:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}